/**
 * React 19 Compatibility Shim for use-sync-external-store
 * 
 * React 19 has useSyncExternalStore built-in, so we don't need the external package.
 * This shim provides the same API that Zustand expects from use-sync-external-store.
 */

import { useSyncExternalStore } from 'react';

// Main export - React 19's built-in useSyncExternalStore
export default useSyncExternalStore;

// Named export for compatibility
export { useSyncExternalStore };

// For libraries that expect the shim structure
export const useSyncExternalStoreWithSelector = useSyncExternalStore;
