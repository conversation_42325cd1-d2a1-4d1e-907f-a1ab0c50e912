/**
 * React 19 Compatibility Shim for use-sync-external-store/shim/with-selector
 * 
 * This provides the with-selector functionality that <PERSON>ustand expects,
 * using React 19's built-in useSyncExternalStore.
 */

import { useSyncExternalStore } from 'react';

// Default export that matches the expected API
export default useSyncExternalStore;

// Named exports for compatibility
export { useSyncExternalStore };
export const useSyncExternalStoreWithSelector = useSyncExternalStore;
