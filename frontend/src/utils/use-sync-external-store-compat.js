/**
 * Compatibility shim for use-sync-external-store/shim/with-selector.js
 *
 * Fixes the default export issue where Zustand's traditional.mjs expects
 * a default export but the package only provides named exports.
 *
 * This file provides the correct export structure that <PERSON>ustand expects.
 */

// Import React's built-in useSyncExternalStore for React 18
import { useSyncExternalStore } from 'react';

// Create a simple implementation of useSyncExternalStoreWithSelector
// This is a simplified version that works with React 18
function useSyncExternalStoreWithSelector(
  subscribe,
  getSnapshot,
  getServerSnapshot,
  selector,
  isEqual
) {
  // For React 18, we can use the built-in useSyncExternalStore
  // and apply the selector manually
  const snapshot = useSyncExternalStore(subscribe, getSnapshot, getServerSnapshot);

  // Apply selector if provided
  if (selector) {
    return selector(snapshot);
  }

  return snapshot;
}

// Export the named export as both named and default export
export { useSyncExternalStoreWithSelector };

// Provide the default export that Zustand's traditional.mjs expects
export default {
  useSyncExternalStoreWithSelector
};
