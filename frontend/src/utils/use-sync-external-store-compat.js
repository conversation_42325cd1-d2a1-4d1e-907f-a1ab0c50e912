/**
 * Compatibility shim for use-sync-external-store/shim/with-selector.js
 * 
 * Fixes the default export issue where Zustand's traditional.mjs expects
 * a default export but the package only provides named exports.
 * 
 * This file provides the correct export structure that Zustand expects.
 */

import { useSyncExternalStoreWithSelector } from 'use-sync-external-store/shim/with-selector';

// Export the named export as both named and default export
export { useSyncExternalStoreWithSelector };

// Provide the default export that Zustand's traditional.mjs expects
export default {
  useSyncExternalStoreWithSelector
};
