{"name": "vpn-dashboard-frontend", "version": "0.1.0", "private": true, "packageManager": "pnpm@9.0.0", "scripts": {"dev": "vite", "dev:fast": "vite --force --mode development", "dev:minimal": "VITE_MINIMAL=true vite --force --mode development", "dev:localhost": "./scripts/dev-localhost.sh", "dev:https": "./scripts/dev-https.sh", "start": "vite --host 0.0.0.0 --port 3000", "build": "tsc && vite build", "build:fast": "vite build --mode production --minify false", "build:analyze": "vite build && npx rollup-plugin-visualizer --output dist/bundle-analysis.html --open", "preview": "vite preview", "type-check": "tsc --noEmit", "type-check:watch": "tsc --noEmit --watch", "lint": "eslint src --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "lint:fix": "eslint src --ext ts,tsx --fix", "format": "prettier --write \"src/**/*.{ts,tsx,js,jsx,json,css,md}\"", "format:check": "prettier --check \"src/**/*.{ts,tsx,js,jsx,json,css,md}\"", "test": "vitest", "test:run": "vitest run", "test:ui": "vitest --ui", "test:coverage": "vitest run --coverage", "analyze": "vite-bundle-analyzer dist/stats.html", "lighthouse": "lighthouse http://localhost:3000 --output=html --output-path=./lighthouse-report.html", "clean": "rm -rf dist node_modules/.vite", "clean:deps": "rm -rf node_modules/.vite/deps*", "reinstall": "rm -rf node_modules pnpm-lock.yaml && pnpm install"}, "dependencies": {"@heroicons/react": "^2.2.0", "@react-three/drei": "^10.1.2", "@react-three/fiber": "^9.1.2", "@sentry/browser": "^9.26.0", "@sentry/react": "^9.26.0", "@sentry/tracing": "^7.120.3", "@tanstack/react-query": "^5.80.3", "@tanstack/react-query-devtools": "^5.80.3", "@telegram-apps/sdk-react": "^3.3.0", "@types/crypto-js": "^4.2.2", "@types/js-cookie": "^3.0.6", "@types/lodash.debounce": "^4.0.9", "@types/qrcode.react": "^3.0.0", "@types/three": "^0.177.0", "axios": "^1.9.0", "clsx": "^2.1.1", "crypto-js": "^4.2.0", "date-fns": "^4.1.0", "dompurify": "^3.2.6", "emoji-picker-react": "^4.12.2", "fabric": "^6.6.7", "framer-motion": "^12.16.0", "js-cookie": "^3.0.5", "lodash.debounce": "^4.0.8", "lucide-react": "^0.513.0", "qrcode.react": "^4.2.0", "react": "^19.1.0", "react-canvas-draw": "^1.2.1", "react-colorful": "^5.6.1", "react-dom": "^19.1.0", "react-hook-form": "^7.57.0", "react-hot-toast": "^2.5.2", "react-icons": "^5.5.0", "react-router-dom": "^7.6.2", "react-select": "^5.10.1", "react-type-animation": "^3.2.0", "tailwind-merge": "^3.3.0", "three": "^0.177.0", "three-stdlib": "^2.36.0", "web-vitals": "^5.0.2", "zod": "^3.25.51", "zustand": "^5.0.5"}, "devDependencies": {"@eslint/js": "^9.28.0", "@tailwindcss/postcss": "^4.1.8", "@tailwindcss/vite": "^4.1.8", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^14.6.1", "@types/node": "^22.15.29", "@types/react": "^19.1.6", "@types/react-canvas-draw": "^1.2.4", "@types/react-dom": "^19.1.6", "@typescript-eslint/eslint-plugin": "^8.33.1", "@typescript-eslint/parser": "^8.33.1", "@vitejs/plugin-react": "^4.5.1", "@vitejs/plugin-react-swc": "^3.10.1", "@vitest/coverage-v8": "^3.2.1", "@vitest/ui": "^3.2.1", "autoprefixer": "^10.4.21", "eslint": "^9.28.0", "eslint-plugin-react": "^7.37.5", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.20", "globals": "^16.2.0", "jsdom": "^26.1.0", "lighthouse": "^12.6.1", "postcss": "^8.5.4", "prettier": "^3.5.3", "rollup-plugin-visualizer": "^6.0.1", "tailwindcss": "^4.1.8", "typescript": "^5.8.3", "vite": "^6.3.5", "vite-bundle-analyzer": "^0.22.0", "vite-tsconfig-paths": "^5.1.4", "vitest": "^3.2.1", "webpack-bundle-analyzer": "^4.10.2"}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "eslintConfig": {"extends": ["react-app"], "rules": {}, "parserOptions": {"ecmaVersion": 2020}}, "type": "module", "engines": {"node": ">=18.0.0", "pnpm": ">=8.0.0"}, "trustedDependencies": ["@sentry/browser", "@sentry/react"], "pnpm": {"overrides": {"@types/react": "^19.1.6", "@types/react-dom": "^19.1.5", "react-reconciler": "^0.29.2", "react": "^19.1.0", "react-dom": "^19.1.0"}}}