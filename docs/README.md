# Task API Test Suite

This directory contains test scripts for the Task API system, including daily tasks, security features, and general API functionality.

## Enhanced Test Features

- **Schema Validation**: All API responses are validated against Pydantic schemas to ensure data integrity
- **User Authentication**: Tests include user login validation and token verification
- **Daily Task Testing**: Comprehensive testing of daily check-in functionality, streaks, and rewards
- **Security Testing**: Tests for rate limiting, token validation, and other security features
- **Performance Testing**: Tests for API performance under various conditions

## Test Scripts

- `daily_task_test.py` - Tests daily check-in functionality, streaks, and reward scaling
- `security_task_test.py` - Tests security features like rate limiting and token validation
- `task_api_test.py` - Tests general task API functionality
- `api_task_flow_test.py` - Tests complete task flows from start to completion
- `task_performance_test.py` - Tests API performance under various conditions

## Running Tests

Use the `run_task_tests.sh` script to run tests:

```bash
# Run all tests
./run_task_tests.sh --all

# Run specific test
./run_task_tests.sh --daily
./run_task_tests.sh --security
./run_task_tests.sh --core

# Run with custom API URL
./run_task_tests.sh --url http://api.example.com --daily

# Run without schema validation
./run_task_tests.sh --daily --no-schema
```

## Test Environment

Tests require the following environment:

- Python 3.8+
- FastAPI backend running
- Required packages: httpx, rich, pydantic, fastapi

## Test Configuration

Tests can be configured using environment variables:

- `API_BASE_URL` - Base URL for API requests (default: http://localhost:8000)
- `TEST_MODE` - Enable test mode (default: true)
- `ADMIN_USERNAME` - Admin username for tests (default: admin)
- `ADMIN_PASSWORD` - Admin password for tests (default: admin)
- `USER_USERNAME` - User username for tests (default: user)
- `USER_PASSWORD` - User password for tests (default: user)

## Logs

Test logs are stored in the `logs` directory. Use the `cleanup_logs.sh` script to clean up old logs.

## Schema Validation

The test suite now includes comprehensive schema validation for all API responses. This ensures that the API returns data in the expected format and with the expected types. Schema validation is performed using Pydantic models imported from the backend codebase.

Key schema validations include:

- Authentication tokens (Token schema)
- User data (UserOut schema)
- Task data (TaskOut schema)
- Daily check-in responses (DailyCheckInResponse schema)
- Streak information (DailyTaskStreakOut schema)

## User Authentication Testing

The test suite now includes comprehensive testing of user authentication:

- Admin login
- User creation and login
- Token validation
- Authentication headers

## Daily Task Testing

The daily task tests now include:

- User login and authentication
- Schema validation for all responses
- Streak progression simulation
- Cycle completion and bonus rewards
- Streak reset functionality
- Wallet balance verification

## Test Reports

Each test run generates a detailed report including:

- Tests run, passed, and failed
- Schema validations performed
- Schema validation errors
- Success rate

## Troubleshooting

If tests fail, check the following:

1. Ensure the API is running and accessible
2. Check that the database is properly initialized
3. Verify that all required packages are installed
4. Check the logs for detailed error messages 