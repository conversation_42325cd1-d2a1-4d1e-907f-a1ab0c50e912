# Backend Environment
Python version: Python 3.10.12
Pip version: pip 22.0.2 from /root/project/backend/venv/lib/python3.10/site-packages/pip (python 3.10)

# Dependencies
aiofiles==24.1.0
aiogram==3.17.0
aiohappyeyeballs==2.4.4
aiohttp==3.11.11
aiosignal==1.3.2
aiosqlite==0.21.0
alembic==1.14.1
annotated-types==0.7.0
anyio==4.8.0
async-timeout==5.0.1
attrs==24.3.0
bcrypt==4.0.1
cachetools==5.5.1
certifi==2024.12.14
cffi==1.17.1
charset-normalizer==3.4.1
click==8.1.8
colorama==0.4.6
contourpy==1.3.1
coverage==7.6.12
cryptography==44.0.0
cycler==0.12.1
Deprecated==1.2.15
ecdsa==0.19.0
exceptiongroup==1.2.2
fastapi==0.115.7
fonttools==4.56.0
frozenlist==1.5.0
greenlet==3.1.1
h11==0.14.0
hiredis==2.3.2
httpcore==1.0.7
httpx==0.28.1
idna==3.10
iniconfig==2.0.0
itsdangerous==2.2.0
kiwisolver==1.4.8
limits==4.0.1
magic-filter==1.0.12
Mako==1.3.8
markdown-it-py==3.0.0
MarkupSafe==3.0.2
matplotlib==3.10.1
mdurl==0.1.2
multidict==6.1.0
nest-asyncio==1.6.0
numpy==2.2.3
packaging==24.2
pandas==2.2.3
passlib==1.7.4
pillow==11.1.0
pluggy==1.5.0
prometheus_client==0.21.1
propcache==0.2.1
psutil==6.1.1
pyasn1==0.6.1
pycparser==2.22
pydantic==2.10.5
pydantic_core==2.27.2
Pygments==2.19.1
pyparsing==3.2.1
pytest==8.3.4
pytest-asyncio==0.25.3
pytest-cov==6.0.0
python-dateutil==2.9.0.post0
python-dotenv==1.0.1
python-jose==3.3.0
python-multipart==0.0.20
python-telegram-bot==21.10
pytz==2025.1
redis==5.0.1
requests==2.32.3
rich==13.9.4
rsa==4.9
seaborn==0.13.2
six==1.17.0
slowapi==0.1.9
sniffio==1.3.1
SQLAlchemy==2.0.37
starlette==0.45.2
tomli==2.2.1
typing_extensions==4.12.2
tzdata==2025.1
urllib3==2.3.0
uvicorn==0.34.0
wrapt==1.17.2
yarl==1.18.3
