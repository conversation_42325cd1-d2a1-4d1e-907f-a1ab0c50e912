# Metaverse Grid Game Feature: High-Level Plan

This document outlines a high-level technical plan for implementing a metaverse grid-based strategy game feature using FastAPI and related technologies.

## Core Concepts

1.  **Metaverse Grid:** A persistent world map composed of ownable/interactable grid squares.
2.  **User Basements:** Player-owned grids with specific attributes (defense/attack levels, buildings).
3.  **Buildings & Troops:** Structures provide benefits (resource generation, defense, troop capacity) and units are used for attacks/defense.
4.  **Resource Management:** In-game currency is required for building, upgrading, and troop creation.
5.  **Asynchronous Combat:** Time-based attacks initiated by players, resolved on the backend, with potential looting.

## High-Level Backend Architecture (FastAPI)

Structure using distinct services, data models, and routers, adhering to FastAPI best practices (async, Pydantic, dependency injection).

### 1. Data Models (`models.py` & `schemas.py`)

*   **`Grid`:**
    *   `id` (PK)
    *   `x_coord`, `y_coord` (INT, Unique Constraint)
    *   `owner_id` (FK to `User`, nullable, Indexed) - *Who owns this grid.*
    *   `grid_type` (ENUM: 'Empty', 'PlayerBasement', 'ResourceNode', 'ProtectedZone', etc.)
    *   `last_looted_at` (DateTime, nullable) - *For empty grids.*
    *   *(Optional: Terrain type, resource data)*
*   **`UserBasement`:** (Linked one-to-one with a `Grid` owned by a player)
    *   `grid_id` (PK, FK to `Grid`)
    *   `user_id` (FK to `User`, Indexed)
    *   `defense_level` (INT, default 1)
    *   `attack_level` (INT, default 1)
    *   *(Add FKs to buildings, troops associated with this basement, or link via separate tables)*
*   **`Building`:**
    *   `id` (PK)
    *   `basement_grid_id` (FK to `UserBasement`/`Grid`, Indexed)
    *   `building_type` (ENUM: 'Barracks', 'ResourceGenerator', 'Wall', 'Turret', etc.)
    *   `level` (INT, default 1)
    *   `position_x`, `position_y` (INT, optional for visual placement within the grid)
    *   `build_completes_at` (DateTime, nullable) - *Timestamp for when construction/upgrade finishes.*
*   **`Troop`:** (Represents troop counts within a basement)
    *   `id` (PK)
    *   `basement_grid_id` (FK to `UserBasement`/`Grid`, Indexed)
    *   `troop_type` (ENUM: 'Infantry', 'Tank', 'Scout', etc.)
    *   `count` (INT, default 0)
*   **`Attack`:**
    *   `id` (PK)
    *   `attacker_grid_id` (FK to `Grid`, Indexed)
    *   `defender_grid_id` (FK to `Grid`, Indexed)
    *   `status` (ENUM: 'Traveling', 'Fighting', 'Returning', 'Completed', 'Cancelled')
    *   `start_time` (DateTime)
    *   `arrival_time` (DateTime)
    *   `return_time` (DateTime, nullable)
    *   `involved_troops` (JSONB/Text) - *Snapshot of troops sent.*
    *   `loot_result` (JSONB/Text, nullable) - *Resources gained/lost.*
    *   `combat_log` (Text, nullable)
*   **`UserResource`:** (Could extend existing User model or be separate)
    *   `user_id` (PK/FK to `User`)
    *   `currency` (Numeric/Decimal, default 0)
*   **`BuildQueue`, `TroopQueue`:** (Optional, could manage timers directly on `Building`/`Troop` or use separate tables/Redis structures)
    *   Track items currently being built/trained with completion timestamps.

### 2. API Endpoints (FastAPI Routers - `routers/`)

*   **`world_router.py`:**
    *   `GET /world/map`: Fetch grid data for a specific viewport (coordinates, owner, type). Needs pagination/chunking.
    *   `GET /world/grid/{grid_id}`: Get details of a specific grid square.
    *   `POST /world/grid/{grid_id}/claim`: Allow users to claim/buy an empty grid (if applicable).
*   **`basement_router.py`:** (Requires authentication)
    *   `GET /basement/{grid_id}`: Get detailed state of the player's owned basement (levels, buildings, troops).
    *   `POST /basement/{grid_id}/upgrade`: Initiate defense/attack level upgrade (checks resources, starts timer).
    *   `POST /basement/{grid_id}/build`: Initiate building construction/upgrade (checks resources, placement rules, starts timer).
    *   `POST /basement/{grid_id}/train`: Initiate troop training (checks resources, capacity, starts timer).
    *   `GET /basement/{grid_id}/queue`: Get current build/training queue status.
*   **`combat_router.py`:** (Requires authentication)
    *   `POST /combat/attack`: Initiate an attack (takes target `grid_id`, troop composition; performs validation, creates `Attack` record, calculates travel time).
    *   `GET /combat/attacks`: List player's ongoing/recent attacks (status, times).
    *   `GET /combat/attack/{attack_id}`: Get details/log of a specific attack.
*   **`user_router.py` (Existing):**
    *   `GET /user/me/resources`: Get the player's current currency.

### 3. Services (Business Logic - `services/`)

*   **`grid_service.py`:** Logic for fetching map data, handling grid ownership changes.
*   **`basement_service.py`:** Core logic for upgrades, building placement/validation, resource deduction, troop capacity checks, managing build/train queues.
*   **`troop_service.py`:** Definitions of troop stats (attack, defense, speed, cost, training time).
*   **`building_service.py`:** Definitions of building stats, costs, build times, effects.
*   **`combat_service.py`:**
    *   Calculates attack travel times.
    *   Simulates combat based on attacking/defending troops, levels, building bonuses (Core deterministic algorithm needed).
    *   Calculates loot based on defender's resources and battle outcome.
    *   Updates attack status and applies results (resource transfers, troop losses).
*   **`resource_service.py`:** Handles safe debiting/crediting of user currency.
*   **`realtime_service.py` (WebSocket Integration):**
    *   Manages WebSocket connections (`/ws/{user_id}`).
    *   Pushes updates to relevant users: attack started/completed, build finished, resources updated, attacked notification.

### 4. Asynchronous Processing & Real-time Updates

*   **FastAPI Background Tasks:** Use for simple, short-lived post-request actions (e.g., immediate notifications).
*   **Celery (or alternatives like Dramatiq/RQ):** Essential for handling time-based events reliably:
    *   **Combat Resolution:** Schedule task for `Attack.arrival_time` -> runs `combat_service.resolve_combat(attack_id)`.
    *   **Build/Train Completion:** Schedule task for `Building/Troop.build_completes_at`.
    *   **Returning Troops:** Schedule task for `Attack.return_time`.
*   **WebSockets (FastAPI built-in or `python-socketio`):**
    *   `realtime_service` listens for events (e.g., from Celery tasks) and pushes targeted messages to connected clients.

## Key Implementation Details & Challenges

*   **Combat Logic:** Design a clear, deterministic algorithm factoring in troops, levels, buildings. Define loot calculation.
*   **State Management:** Database is the source of truth. Use real-time updates for frontend notifications, but rely on backend state.
*   **Scalability:** Use async DB drivers (`asyncpg`), optimize queries (indexes), cache static data (Redis), distribute background tasks (Celery).
*   **Concurrency:** Use DB transactions and potentially locking (row-level or Redis locks) for critical operations like combat resolution and resource changes.
*   **Security:** Rigorously validate all inputs on the backend. Ensure actions are authorized and affordable. Rely on backend timers.

## Frontend Considerations (React)

*   **Visualization:** Choose 2D (SVG, Canvas, PixiJS) or 3D (Three.js, Babylon.js). Start simple.
*   **State Management:** Use Zustand, Redux Toolkit, etc., for game state.
*   **WebSocket Client:** Use `socket.io-client` or native WebSockets.
*   **UX:** Provide clear feedback for timers, reports, resource changes. 