# Scaling Configuration for 500 Concurrent Users
# Last Updated: 2024-01-02

1. GUNICORN CONFIGURATION
========================
Gun<PERSON> acts as a process manager for FastAPI:
- Manages multiple worker processes
- Distributes incoming requests
- Handles connection queuing

Configuration (gunicorn_conf.py):
```python
bind = "0.0.0.0:8000"
backlog = 2048        # Request queue size
workers = 4           # CPU cores * 2 + 1
worker_class = "uvicorn.workers.UvicornWorker"
worker_connections = 1000
timeout = 120
keepalive = 5
```

2. HARDWARE REQUIREMENTS
=======================
Recommended Server Specifications:
- CPU: 4 cores (8 threads)
- RAM: 8GB
- SSD: 50GB
- Network: 100Mbps

This setup can handle:
- 500 concurrent users
- ~1000 requests per minute
- Database with 100,000+ records

3. FASTAPI IMPLEMENTATION
========================
Main Application (main.py):
```python
from fastapi import <PERSON><PERSON><PERSON>, Request, HTTPException
from fastapi.middleware.cors import CORSMiddleware
import uvicorn
from contextlib import asynccontextmanager
import time

@asynccontextmanager
async def lifespan(app: FastAPI):
    # Startup
    setup_database_pool()
    yield
    # Shutdown
    cleanup_connections()

app = FastAPI(lifespan=lifespan)

# Basic rate limiting
RATE_LIMIT = 60  # requests per minute
rate_limits = {}

@app.middleware("http")
async def rate_limit_middleware(request: Request, call_next):
    client_ip = request.client.host
    current_time = time.time()
    
    # Clean old requests
    if client_ip in rate_limits:
        rate_limits[client_ip] = [
            req_time for req_time in rate_limits[client_ip]
            if current_time - req_time < 60
        ]
    else:
        rate_limits[client_ip] = []
    
    # Check rate limit
    if len(rate_limits[client_ip]) >= RATE_LIMIT:
        raise HTTPException(status_code=429, detail="Too many requests")
    
    rate_limits[client_ip].append(current_time)
    return await call_next(request)
```

4. DATABASE CONFIGURATION
========================
Database Setup (database.py):
```python
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker

engine = create_engine(
    "postgresql://user:pass@localhost/db",
    pool_size=20,        # Maximum number of database connections
    max_overflow=10,     # Extra connections when pool is full
    pool_timeout=30,     # Seconds to wait for available connection
    pool_recycle=1800    # Refresh connections every 30 minutes
)

SessionLocal = sessionmaker(bind=engine)

def get_db():
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()
```

5. DEPLOYMENT SETUP
==================
SystemD Service Configuration:
```ini
# /etc/systemd/system/fastapi.service
[Unit]
Description=FastAPI Application
After=network.target

[Service]
User=www-data
Group=www-data
WorkingDirectory=/path/to/app
ExecStart=/path/to/gunicorn -c gunicorn_conf.py main:app
Restart=always

[Install]
WantedBy=multi-user.target
```

6. NGINX CONFIGURATION
=====================
```nginx
# /etc/nginx/sites-available/fastapi
server {
    listen 80;
    server_name your_domain.com;

    location / {
        proxy_pass http://127.0.0.1:8000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_cache_bypass $http_upgrade;
        
        # Timeouts
        proxy_connect_timeout 60s;
        proxy_send_timeout 60s;
        proxy_read_timeout 60s;
    }
}
```

7. MONITORING METRICS
====================
Key Metrics to Monitor:
1. CPU Usage:
   - Warning threshold: 70%
   - Critical threshold: 85%

2. Memory Usage:
   - Warning threshold: 80%
   - Critical threshold: 90%

3. Database Connections:
   - Max connections: 30
   - Warning threshold: 25 connections
   - Monitor for connection spikes

4. Response Times:
   - Target: < 500ms
   - Warning threshold: > 1s
   - Monitor p95 and p99 latencies

8. STARTUP COMMANDS
==================
```bash
# Start application
sudo systemctl start fastapi

# Monitor logs
tail -f /var/log/fastapi/access.log

# Check status
sudo systemctl status fastapi
```

9. REQUIRED PACKAGES
===================
Add to requirements.txt:
```
fastapi==0.104.1
uvicorn[standard]==0.24.0
gunicorn==21.2.0
sqlalchemy==2.0.23
psycopg2-binary==2.9.9
```

10. PERFORMANCE NOTES
====================
This setup is optimized for:
- 500 concurrent users
- No complex queuing needed
- Simple rate limiting sufficient
- Basic connection pooling adequate

Key Points:
1. No Redis needed at this scale
2. In-memory rate limiting is sufficient
3. Focus on database connection management
4. Monitor response times closely

Scaling Triggers:
- CPU consistently above 70%
- Memory usage above 80%
- Response times above 500ms
- Database connections near max

When these triggers are hit, consider:
1. Increasing hardware resources
2. Adding database read replicas
3. Implementing caching
4. Horizontal scaling 