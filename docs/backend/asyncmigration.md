# Backend Async Migration and Rate Limiting Plan

This document outlines the steps required to migrate the FastAPI backend from synchronous to asynchronous database operations to resolve performance bottlenecks and resource exhaustion issues (like "Too many open files"). It also discusses strategies for implementing rate limiting.

## 1. Rationale

The primary issue identified is the use of synchronous database calls (`Depends(get_db)`) within FastAPI's asynchronous environment. This blocks the event loop, leading to inefficient worker utilization and exhaustion of file descriptors under load.

Migrating to fully asynchronous database operations (`Depends(get_async_db)`, `async`/`await`) is essential for performance, scalability, and stability.

## 2. Migration Steps

The core task involves refactoring all API routes that interact with the database.

**General Steps for Each Router File** (`backend/routers/*.py`):

1.  **Change Imports:**
    *   Replace `from sqlalchemy.orm import Session` with `from sqlalchemy.ext.asyncio import AsyncSession`.
    *   Replace `from database import get_db` with `from database import get_async_db`.
2.  **Update Route Signatures:**
    *   Change `def route_function(...)` to `async def route_function(...)`.
    *   Change `db: Session = Depends(get_db)` to `db: AsyncSession = Depends(get_async_db)`.
3.  **Update Database Calls:**
    *   Prefix all `db.execute(...)` calls with `await`: `result = await db.execute(...)`.
    *   Prefix all `db.commit()` calls with `await`: `await db.commit()`.
    *   Prefix all `db.rollback()` calls with `await`: `await db.rollback()`.
    *   Prefix all `db.refresh(...)` calls with `await`: `await db.refresh(...)`.
    *   Prefix all `db.get(...)` calls with `await`: `instance = await db.get(...)`.
    *   **Important:** `db.add(instance)` remains **synchronous** and does **not** need `await`.
    *   SQLAlchemy result processing methods (`.scalars()`, `.scalar()`, `.all()`, `.first()`, `.one()`, `.one_or_none()`) are called *after* `await db.execute()` and remain synchronous.
4.  **Update Eager Loading (if necessary):**
    *   If using `joinedload` or `subqueryload` within route logic (less common), ensure they are compatible or consider using `selectinload` which is generally preferred for async. Note: `options(...)` are added *before* the `await db.execute()`.
5.  **Refactor Dependencies:**
    *   Review dependencies used by the routes (e.g., `auth.get_current_active_user`, `auth.get_current_admin`). If these dependencies *also* perform database I/O, they **must** be refactored to be asynchronous (`async def`) as well, otherwise they will block the event loop when called.

**Target Router Files (Based on previous `grep` search):**

*   [x] `backend/routers/card_router.py` (Completed)
*   [ ] `backend/routers/payment.py`
*   [ ] `backend/routers/auth_router.py` (Pay close attention to dependencies like `get_current_active_user`)
*   [x] `backend/routers/task_router.py` (Completed)
*   [x] `backend/routers/monitoring_router.py` (Completed)
*   [x] `backend/routers/daily_task_router.py` (Completed)
*   [x] `backend/routers/referral_router.py` (Completed)
*   [x] `backend/routers/user_dashboard.py` (Completed)
*   [x] `backend/routers/security_router.py` (Completed)
*   [x] `backend/routers/marzban_router.py` (Completed)

**Additional Migrations:**

* [x] Moved user dashboard endpoints from `main.py` to `backend/routers/user_dashboard.py`:
  * `/users/me/` → `/api/user/me`
  * `/users/stats/` → `/api/user/stats`
  * `/users/{user_id}/purchase/{package_id}` → `/api/user/purchase/{package_id}`
  * `/subscriptions/{subscription_id}/usage` → (Using existing `/api/user/subscriptions/{subscription_id}/usage`)
  * `/users/telegram-data/update` → `/api/user/telegram-data/update`

**Verification:**

*   After refactoring, run application tests (if any).
*   Perform load testing to ensure the "Too many open files" error is resolved and performance has improved.
*   Monitor resource usage (file descriptors, CPU, memory) under load.

## 3. Rate Limiting Strategy

Implementing rate limiting is crucial for protecting the application from abuse, ensuring fair usage, and maintaining stability, especially for a game-like application with potentially frequent actions.

**General Approach:**

*   **Use Redis:** A centralized cache like Redis is highly recommended for efficient, distributed rate limiting across multiple worker processes/instances. Avoid using in-memory dictionaries or simple database checks for high-traffic endpoints.
*   **Identify Key Actions:** Determine which actions need rate limiting (e.g., claiming profit, upgrading, buying, potentially frequent read operations if they are heavy).
*   **Choose Algorithm:** The "Token Bucket" or "Fixed Window Counter" / "Sliding Window Log" algorithms are common choices. Start simple (Fixed Window) if requirements allow.
    *   **Fixed Window:** Simple to implement (e.g., allow X requests per minute per user/IP). Reset counter every minute.
    *   **Sliding Window / Token Bucket:** More complex but smoother; avoids bursts at the window edge. Libraries often abstract this.
*   **Keying:** Decide how to track limits (per user ID, per IP address, or a combination). For game actions like upgrades/purchases, per-user ID is essential. Per-IP can help mitigate anonymous abuse.
*   **Implementation:**
    *   **Middleware:** Apply global rate limits (e.g., general API usage per IP) using FastAPI middleware.
    *   **Dependencies:** Implement action-specific rate limits (e.g., "claim profit" per user) using FastAPI Dependencies. This allows injecting the rate check logic directly into the routes that need it.
    *   **Libraries:** Consider using libraries like `fastapi-limiter` which integrates with Redis and provides decorators/dependencies.

**Specific Rate Limiting Ideas:**

*   **`/api/cards/claim-all`:**
    *   Limit: Strict limit per user (e.g., 1-3 times per minute) to prevent spamming claims.
    *   Key: `rate_limit:claim:{user_id}`
*   **`/api/cards/{user_card_id}/upgrade`:**
    *   Limit: Moderate limit per user (e.g., 10-20 times per minute) to allow for reasonable interaction but prevent abuse scripts.
    *   Key: `rate_limit:upgrade:{user_id}`
*   **`/api/cards/buy/{card_catalog_id}`:**
    *   Limit: Moderate limit per user (similar to upgrade).
    *   Key: `rate_limit:buy:{user_id}`
*   **`/api/auth/token` (Login):**
    *   Limit: Stricter limit per IP address *and* potentially per username on failure to prevent brute-forcing.
    *   Key: `rate_limit:login_ip:{ip_address}`, `rate_limit:login_fail_user:{username}`
*   **Frequent Read Endpoints (e.g., `/my-cards`, `/all`):**
    *   Limit: More lenient limit per user (e.g., 30-60 per minute) unless proven to be a performance issue. Caching responses might be more effective than strict rate limiting here.
    *   Key: `rate_limit:read_cards:{user_id}`

**Error Response:**

*   When a limit is exceeded, return an HTTP `429 Too Many Requests` status code.
*   Optionally include `Retry-After` header to inform the client when they can try again.

## 4. Security Considerations

*   **Rate Limiting:** As discussed, prevents certain DoS/abuse patterns.
*   **Authentication/Authorization:** Ensure `Depends(auth.get_current_active_user)` and admin checks are correctly applied to all sensitive endpoints. Review the `auth.py` module itself for potential vulnerabilities, especially if it was synchronous and needs async refactoring.
    *   **ACTION REQUIRED:** Explicitly review `auth.py` functions used as dependencies (e.g., `get_auth`, `get_current_active_user`, `get_current_admin`, `process_telegram_auth`) to ensure they are `async def` if they perform any database or other blocking I/O operations.
*   **Input Validation:** Rely on Pydantic schemas defined in `schemas.py` for robust input validation.
*   **SQL Injection:** Using SQLAlchemy Core expression language (`select()`, ORM methods) generally prevents SQL injection, but double-check any raw SQL execution (`text()`) if used.
*   **Dependencies:** Keep libraries updated (`requirements.txt`) to patch known vulnerabilities.

By systematically migrating to async operations and implementing thoughtful rate limiting, the application's performance, stability, and security will be significantly enhanced. 