## Table: _alembic_tmp_daily_task_streaks

**Columns:**
- **id**: INTEGER | Nullable: False | Default: None
- **user_id**: INTEGER | Nullable: False | Default: None
- **current_streak**: INTEGER | Nullable: False | Default: None
- **longest_streak**: INTEGER | Nullable: False | Default: None
- **last_check_in**: DATETIME | Nullable: True | Default: None
- **total_check_ins**: INTEGER | Nullable: False | Default: None
- **current_cycle_day**: INTEGER | Nullable: False | Default: None
- **created_at**: DATETIME | Nullable: False | Default: None
- **first_check_time**: DATETIME | Nullable: True | Default: None
- **last_streak_break**: DATETIME | Nullable: True | Default: None
**Primary Key:** ['id']

**Foreign Keys:**
- ['user_id'] -> users(['id'])
**Indexes:** None

---

## Table: alembic_version

**Columns:**
- **version_num**: VARCHAR(32) | Nullable: False | Default: None
**Primary Key:** ['version_num']

**Foreign Keys:** None
**Indexes:** None

---

## Table: daily_task_streaks

**Columns:**
- **id**: INTEGER | Nullable: False | Default: None
- **user_id**: INTEGER | Nullable: False | Default: None
- **current_streak**: INTEGER | Nullable: False | Default: None
- **longest_streak**: INTEGER | Nullable: False | Default: None
- **last_check_in**: DATETIME | Nullable: True | Default: None
- **total_check_ins**: INTEGER | Nullable: False | Default: None
- **current_cycle_day**: INTEGER | Nullable: False | Default: None
- **created_at**: DATETIME | Nullable: False | Default: None
- **first_check_time**: TIMESTAMP | Nullable: True | Default: None
- **last_streak_break**: DATETIME | Nullable: True | Default: None
**Primary Key:** ['id']

**Foreign Keys:**
- ['user_id'] -> users(['id'])
**Indexes:**
- idx_daily_task_streaks_user: Columns: ['user_id'] | Unique: 0
- idx_user_cycle: Columns: ['user_id', 'current_cycle_day', 'last_check_in'] | Unique: 0
- idx_user_streak: Columns: ['user_id', 'last_check_in'] | Unique: 0

---

## Table: marzban_panels

**Columns:**
- **id**: INTEGER | Nullable: False | Default: None
- **name**: VARCHAR(100) | Nullable: False | Default: None
- **api_url**: VARCHAR(255) | Nullable: False | Default: None
- **admin_username**: VARCHAR(100) | Nullable: False | Default: None
- **admin_password**: VARCHAR(255) | Nullable: False | Default: None
- **is_active**: BOOLEAN | Nullable: False | Default: None
- **created_at**: DATETIME | Nullable: False | Default: None
**Primary Key:** ['id']

**Foreign Keys:** None
**Indexes:**
- ix_marzban_panels_id: Columns: ['id'] | Unique: 0

---

## Table: package_panel_association

**Columns:**
- **package_id**: INTEGER | Nullable: True | Default: None
- **panel_id**: INTEGER | Nullable: True | Default: None
**Primary Key:** []

**Foreign Keys:**
- ['package_id'] -> vpn_packages(['id'])
- ['panel_id'] -> marzban_panels(['id'])
**Indexes:** None

---

## Table: rate_limits

**Columns:**
- **id**: INTEGER | Nullable: False | Default: None
- **user_id**: INTEGER | Nullable: True | Default: None
- **action_type**: VARCHAR(50) | Nullable: False | Default: None
- **ip_address**: VARCHAR(45) | Nullable: True | Default: None
- **timestamp**: DATETIME | Nullable: False | Default: None
- **success**: BOOLEAN | Nullable: False | Default: None
- **details**: TEXT | Nullable: True | Default: None
**Primary Key:** ['id']

**Foreign Keys:**
- ['user_id'] -> users(['id'])
**Indexes:**
- idx_rate_limit_ip_action: Columns: ['ip_address', 'action_type'] | Unique: 0
- idx_rate_limit_timestamp: Columns: ['timestamp'] | Unique: 0
- idx_rate_limit_user_action: Columns: ['user_id', 'action_type'] | Unique: 0
- ix_rate_limits_action_type: Columns: ['action_type'] | Unique: 0
- ix_rate_limits_id: Columns: ['id'] | Unique: 0
- ix_rate_limits_ip_address: Columns: ['ip_address'] | Unique: 0

---

## Table: security_logs

**Columns:**
- **id**: INTEGER | Nullable: True | Default: None
- **action_type**: VARCHAR(50) | Nullable: False | Default: None
- **user_id**: INTEGER | Nullable: True | Default: None
- **ip_address**: VARCHAR(45) | Nullable: False | Default: None
- **user_agent**: VARCHAR(255) | Nullable: True | Default: None
- **status**: VARCHAR(20) | Nullable: False | Default: None
- **details**: JSON | Nullable: True | Default: None
- **risk_level**: VARCHAR(10) | Nullable: False | Default: None
- **created_at**: TIMESTAMP | Nullable: False | Default: CURRENT_TIMESTAMP
- **attempt_count**: INTEGER | Nullable: True | Default: 1
- **window_start**: TIMESTAMP | Nullable: True | Default: None
- **blocked_until**: TIMESTAMP | Nullable: True | Default: None
- **is_blocked**: BOOLEAN | Nullable: True | Default: FALSE
- **location_info**: JSON | Nullable: True | Default: None
**Primary Key:** ['id']

**Foreign Keys:**
- ['user_id'] -> users(['id'])
**Indexes:** None

---

## Table: security_logs_backup_20250205_003425

**Columns:**
- **id**: INTEGER | Nullable: True | Default: None
- **action_type**: TEXT | Nullable: True | Default: None
- **user_id**: INTEGER | Nullable: True | Default: None
- **ip_address**: TEXT | Nullable: True | Default: None
- **user_agent**: TEXT | Nullable: True | Default: None
- **status**: TEXT | Nullable: True | Default: None
- **details**: NUMERIC | Nullable: True | Default: None
- **risk_level**: TEXT | Nullable: True | Default: None
- **created_at**: NUMERIC | Nullable: True | Default: None
- **attempt_count**: INTEGER | Nullable: True | Default: None
- **window_start**: NUMERIC | Nullable: True | Default: None
- **blocked_until**: NUMERIC | Nullable: True | Default: None
- **is_blocked**: NUMERIC | Nullable: True | Default: None
**Primary Key:** []

**Foreign Keys:** None
**Indexes:** None

---

## Table: security_logs_backup_20250205_003436

**Columns:**
- **id**: INTEGER | Nullable: True | Default: None
- **action_type**: TEXT | Nullable: True | Default: None
- **user_id**: INTEGER | Nullable: True | Default: None
- **ip_address**: TEXT | Nullable: True | Default: None
- **user_agent**: TEXT | Nullable: True | Default: None
- **status**: TEXT | Nullable: True | Default: None
- **details**: NUMERIC | Nullable: True | Default: None
- **risk_level**: TEXT | Nullable: True | Default: None
- **created_at**: NUMERIC | Nullable: True | Default: None
- **attempt_count**: INTEGER | Nullable: True | Default: None
- **window_start**: NUMERIC | Nullable: True | Default: None
- **blocked_until**: NUMERIC | Nullable: True | Default: None
- **is_blocked**: NUMERIC | Nullable: True | Default: None
**Primary Key:** []

**Foreign Keys:** None
**Indexes:** None

---

## Table: security_logs_backup_20250205_003440

**Columns:**
- **id**: INTEGER | Nullable: True | Default: None
- **action_type**: TEXT | Nullable: True | Default: None
- **user_id**: INTEGER | Nullable: True | Default: None
- **ip_address**: TEXT | Nullable: True | Default: None
- **user_agent**: TEXT | Nullable: True | Default: None
- **status**: TEXT | Nullable: True | Default: None
- **details**: NUMERIC | Nullable: True | Default: None
- **risk_level**: TEXT | Nullable: True | Default: None
- **created_at**: NUMERIC | Nullable: True | Default: None
- **attempt_count**: INTEGER | Nullable: True | Default: None
- **window_start**: NUMERIC | Nullable: True | Default: None
- **blocked_until**: NUMERIC | Nullable: True | Default: None
- **is_blocked**: NUMERIC | Nullable: True | Default: None
**Primary Key:** []

**Foreign Keys:** None
**Indexes:** None

---

## Table: security_logs_backup_20250205_003446

**Columns:**
- **id**: INTEGER | Nullable: True | Default: None
- **action_type**: TEXT | Nullable: True | Default: None
- **user_id**: INTEGER | Nullable: True | Default: None
- **ip_address**: TEXT | Nullable: True | Default: None
- **user_agent**: TEXT | Nullable: True | Default: None
- **status**: TEXT | Nullable: True | Default: None
- **details**: NUMERIC | Nullable: True | Default: None
- **risk_level**: TEXT | Nullable: True | Default: None
- **created_at**: NUMERIC | Nullable: True | Default: None
- **attempt_count**: INTEGER | Nullable: True | Default: None
- **window_start**: NUMERIC | Nullable: True | Default: None
- **blocked_until**: NUMERIC | Nullable: True | Default: None
- **is_blocked**: NUMERIC | Nullable: True | Default: None
**Primary Key:** []

**Foreign Keys:** None
**Indexes:** None

---

## Table: security_logs_backup_20250205_003450

**Columns:**
- **id**: INTEGER | Nullable: True | Default: None
- **action_type**: TEXT | Nullable: True | Default: None
- **user_id**: INTEGER | Nullable: True | Default: None
- **ip_address**: TEXT | Nullable: True | Default: None
- **user_agent**: TEXT | Nullable: True | Default: None
- **status**: TEXT | Nullable: True | Default: None
- **details**: NUMERIC | Nullable: True | Default: None
- **risk_level**: TEXT | Nullable: True | Default: None
- **created_at**: NUMERIC | Nullable: True | Default: None
- **attempt_count**: INTEGER | Nullable: True | Default: None
- **window_start**: NUMERIC | Nullable: True | Default: None
- **blocked_until**: NUMERIC | Nullable: True | Default: None
- **is_blocked**: NUMERIC | Nullable: True | Default: None
**Primary Key:** []

**Foreign Keys:** None
**Indexes:** None

---

## Table: security_logs_backup_20250205_003721

**Columns:**
- **id**: INTEGER | Nullable: True | Default: None
- **action_type**: TEXT | Nullable: True | Default: None
- **user_id**: INTEGER | Nullable: True | Default: None
- **ip_address**: TEXT | Nullable: True | Default: None
- **user_agent**: TEXT | Nullable: True | Default: None
- **status**: TEXT | Nullable: True | Default: None
- **details**: NUMERIC | Nullable: True | Default: None
- **risk_level**: TEXT | Nullable: True | Default: None
- **created_at**: NUMERIC | Nullable: True | Default: None
- **attempt_count**: INTEGER | Nullable: True | Default: None
- **window_start**: NUMERIC | Nullable: True | Default: None
- **blocked_until**: NUMERIC | Nullable: True | Default: None
- **is_blocked**: NUMERIC | Nullable: True | Default: None
**Primary Key:** []

**Foreign Keys:** None
**Indexes:** None

---

## Table: security_logs_backup_20250205_003737

**Columns:**
- **id**: INTEGER | Nullable: True | Default: None
- **action_type**: TEXT | Nullable: True | Default: None
- **user_id**: INTEGER | Nullable: True | Default: None
- **ip_address**: TEXT | Nullable: True | Default: None
- **user_agent**: TEXT | Nullable: True | Default: None
- **status**: TEXT | Nullable: True | Default: None
- **details**: NUMERIC | Nullable: True | Default: None
- **risk_level**: TEXT | Nullable: True | Default: None
- **created_at**: NUMERIC | Nullable: True | Default: None
- **attempt_count**: INTEGER | Nullable: True | Default: None
- **window_start**: NUMERIC | Nullable: True | Default: None
- **blocked_until**: NUMERIC | Nullable: True | Default: None
- **is_blocked**: NUMERIC | Nullable: True | Default: None
**Primary Key:** []

**Foreign Keys:** None
**Indexes:** None

---

## Table: system_configs

**Columns:**
- **id**: INTEGER | Nullable: False | Default: None
- **key**: VARCHAR(100) | Nullable: False | Default: None
- **value**: TEXT | Nullable: False | Default: None
- **description**: TEXT | Nullable: True | Default: None
- **updated_at**: DATETIME | Nullable: False | Default: None
**Primary Key:** ['id']

**Foreign Keys:** None
**Indexes:**
- idx_system_configs_id: Columns: ['id'] | Unique: 0

---

## Table: task_completions

**Columns:**
- **id**: INTEGER | Nullable: True | Default: None
- **task_id**: INTEGER | Nullable: False | Default: None
- **user_id**: INTEGER | Nullable: False | Default: None
- **status**: VARCHAR | Nullable: False | Default: None
- **started_at**: DATETIME | Nullable: False | Default: None
- **current_progress**: INTEGER | Nullable: False | Default: 0
- **verification_attempts**: INTEGER | Nullable: False | Default: 0
- **is_claimed**: BOOLEAN | Nullable: False | Default: FALSE
- **expires_at**: DATETIME | Nullable: True | Default: None
- **last_verified_at**: DATETIME | Nullable: True | Default: None
- **completed_at**: DATETIME | Nullable: True | Default: None
- **claimed_at**: DATETIME | Nullable: True | Default: None
- **verification_data**: TEXT | Nullable: True | Default: None
- **streak_day**: INTEGER | Nullable: True | Default: None
- **cycle_day**: INTEGER | Nullable: True | Default: None
- **reward_multiplier**: FLOAT | Nullable: True | Default: None
- **reward_amount**: FLOAT | Nullable: False | Default: 0.0
- **verification_ip**: VARCHAR(45) | Nullable: True | Default: None
- **verification_user_agent**: VARCHAR(255) | Nullable: True | Default: None
- **last_verification_attempt**: DATETIME | Nullable: True | Default: None
- **verification_cooldown**: INTEGER | Nullable: False | Default: 60
- **max_verification_attempts**: INTEGER | Nullable: False | Default: 3
- **is_verified**: BOOLEAN | Nullable: False | Default: False
- **verification_method**: VARCHAR(50) | Nullable: True | Default: None
- **verification_token**: VARCHAR(100) | Nullable: True | Default: None
**Primary Key:** ['id']

**Foreign Keys:**
- ['task_id'] -> tasks(['id'])
- ['user_id'] -> users(['id'])
**Indexes:**
- idx_task_completion_task_status: Columns: ['task_id', 'status'] | Unique: 0
- idx_task_completion_user_status: Columns: ['user_id', 'status'] | Unique: 0
- idx_task_verification_completion: Columns: ['task_id', 'status', 'last_verified_at'] | Unique: 0
- idx_user_completion: Columns: ['user_id', 'is_claimed', 'status'] | Unique: 0
- idx_user_task_status: Columns: ['user_id', 'task_id', 'status'] | Unique: 0
- ix_task_completions_id: Columns: ['id'] | Unique: 0
- ix_task_completions_is_claimed: Columns: ['is_claimed'] | Unique: 0
- ix_task_completions_status: Columns: ['status'] | Unique: 0
- ix_task_completions_task_id: Columns: ['task_id'] | Unique: 0
- ix_task_completions_user_id: Columns: ['user_id'] | Unique: 0
- ix_task_completions_user_id_completed_at_streak_day: Columns: ['user_id', 'completed_at', 'streak_day'] | Unique: 0
- ix_task_completions_verification_attempts_last_verification_attempt: Columns: ['verification_attempts', 'last_verification_attempt'] | Unique: 0

---

## Table: task_completions_backup_20250202_011845

**Columns:**
- **id**: INTEGER | Nullable: True | Default: None
- **task_id**: INTEGER | Nullable: True | Default: None
- **user_id**: INTEGER | Nullable: True | Default: None
- **status**: TEXT | Nullable: True | Default: None
- **started_at**: NUMERIC | Nullable: True | Default: None
- **current_progress**: INTEGER | Nullable: True | Default: None
- **verification_attempts**: INTEGER | Nullable: True | Default: None
- **is_claimed**: NUMERIC | Nullable: True | Default: None
- **expires_at**: NUMERIC | Nullable: True | Default: None
- **last_verified_at**: NUMERIC | Nullable: True | Default: None
- **completed_at**: NUMERIC | Nullable: True | Default: None
- **claimed_at**: NUMERIC | Nullable: True | Default: None
- **verification_data**: TEXT | Nullable: True | Default: None
- **streak_day**: INTEGER | Nullable: True | Default: None
- **cycle_day**: INTEGER | Nullable: True | Default: None
- **reward_multiplier**: REAL | Nullable: True | Default: None
**Primary Key:** []

**Foreign Keys:** None
**Indexes:** None

---

## Table: task_pack_tasks

**Columns:**
- **pack_id**: INTEGER | Nullable: False | Default: None
- **task_id**: INTEGER | Nullable: False | Default: None
- **order**: INTEGER | Nullable: False | Default: None
**Primary Key:** ['pack_id', 'task_id']

**Foreign Keys:**
- ['pack_id'] -> task_packs(['id'])
- ['task_id'] -> tasks(['id'])
**Indexes:**
- idx_pack_task_order: Columns: ['pack_id', 'order'] | Unique: 0
- idx_task_pack_tasks_pack: Columns: ['pack_id'] | Unique: 0
- idx_task_pack_tasks_task: Columns: ['task_id'] | Unique: 0
- idx_task_pack_tasks_task_id: Columns: ['task_id'] | Unique: 0

---

## Table: task_packs

**Columns:**
- **id**: INTEGER | Nullable: False | Default: None
- **name**: VARCHAR(100) | Nullable: False | Default: None
- **description**: TEXT | Nullable: False | Default: None
- **reward_package_id**: INTEGER | Nullable: False | Default: None
- **is_lifetime**: BOOLEAN | Nullable: False | Default: None
- **auto_renewal**: BOOLEAN | Nullable: False | Default: None
- **required_duration**: INTEGER | Nullable: False | Default: None
- **is_active**: BOOLEAN | Nullable: False | Default: None
- **created_at**: DATETIME | Nullable: False | Default: None
**Primary Key:** ['id']

**Foreign Keys:**
- ['reward_package_id'] -> vpn_packages(['id'])
**Indexes:**
- idx_task_packs_id: Columns: ['id'] | Unique: 0
- idx_task_packs_reward: Columns: ['reward_package_id'] | Unique: 0
- idx_task_packs_reward_package_id: Columns: ['reward_package_id'] | Unique: 0

---

## Table: task_verifications

**Columns:**
- **id**: INTEGER | Nullable: False | Default: None
- **task_completion_id**: INTEGER | Nullable: False | Default: None
- **status**: VARCHAR | Nullable: False | Default: None
- **verification_data**: TEXT | Nullable: True | Default: None
- **verified_at**: DATETIME | Nullable: False | Default: None
- **next_verification**: DATETIME | Nullable: True | Default: None
- **ip_address**: VARCHAR | Nullable: True | Default: None
- **user_agent**: VARCHAR | Nullable: True | Default: None
- **verification_method**: VARCHAR | Nullable: False | Default: None
- **verification_result**: TEXT | Nullable: True | Default: None
- **created_at**: DATETIME | Nullable: False | Default: None
**Primary Key:** ['id']

**Foreign Keys:**
- ['task_completion_id'] -> task_completions(['id'])
**Indexes:**
- ix_task_verifications_id: Columns: ['id'] | Unique: 0

---

## Table: task_verifications_backup_20250202_011845

**Columns:**
- **id**: INTEGER | Nullable: True | Default: None
- **task_completion_id**: INTEGER | Nullable: True | Default: None
- **status**: TEXT | Nullable: True | Default: None
- **verification_data**: NUMERIC | Nullable: True | Default: None
- **verified_at**: NUMERIC | Nullable: True | Default: None
- **next_verification**: NUMERIC | Nullable: True | Default: None
- **ip_address**: TEXT | Nullable: True | Default: None
- **user_agent**: TEXT | Nullable: True | Default: None
- **verification_method**: TEXT | Nullable: True | Default: None
- **verification_result**: NUMERIC | Nullable: True | Default: None
- **created_at**: NUMERIC | Nullable: True | Default: None
**Primary Key:** []

**Foreign Keys:** None
**Indexes:** None

---

## Table: tasks

**Columns:**
- **id**: INTEGER | Nullable: False | Default: None
- **name**: VARCHAR(100) | Nullable: False | Default: None
- **description**: TEXT | Nullable: False | Default: None
- **type**: VARCHAR(16) | Nullable: False | Default: None
- **reward_type**: VARCHAR(12) | Nullable: False | Default: None
- **reward_value**: FLOAT | Nullable: False | Default: None
- **target_value**: INTEGER | Nullable: False | Default: None
- **is_active**: BOOLEAN | Nullable: False | Default: None
- **created_at**: DATETIME | Nullable: False | Default: None
- **cycle_length**: INTEGER | Nullable: True | Default: None
- **daily_rewards**: TEXT | Nullable: True | Default: None
- **cycle_bonus_reward**: FLOAT | Nullable: True | Default: None
- **reset_streak_after_hours**: INTEGER | Nullable: True | Default: None
- **platform_url**: TEXT | Nullable: True | Default: None
- **platform_id**: VARCHAR(100) | Nullable: True | Default: None
- **verify_key**: VARCHAR(100) | Nullable: True | Default: None
- **max_verification_attempts**: INTEGER | Nullable: False | Default: None
- **verification_cooldown**: INTEGER | Nullable: True | Default: None
**Primary Key:** ['id']

**Foreign Keys:** None
**Indexes:**
- idx_task_daily: Columns: ['type', 'cycle_length'] | Unique: 0
- idx_task_platform: Columns: ['platform_id', 'type'] | Unique: 0
- idx_task_reward: Columns: ['reward_type', 'reward_value'] | Unique: 0
- idx_task_type_active: Columns: ['type', 'is_active'] | Unique: 0
- ix_tasks_id: Columns: ['id'] | Unique: 0
- ix_tasks_is_active: Columns: ['is_active'] | Unique: 0
- ix_tasks_platform_id: Columns: ['platform_id'] | Unique: 0
- ix_tasks_reward_type: Columns: ['reward_type'] | Unique: 0
- ix_tasks_type: Columns: ['type'] | Unique: 0

---

## Table: tasks_backup_20250202_011845

**Columns:**
- **id**: INTEGER | Nullable: True | Default: None
- **name**: TEXT | Nullable: True | Default: None
- **description**: TEXT | Nullable: True | Default: None
- **type**: TEXT | Nullable: True | Default: None
- **reward_type**: TEXT | Nullable: True | Default: None
- **reward_value**: REAL | Nullable: True | Default: None
- **target_value**: INTEGER | Nullable: True | Default: None
- **is_active**: NUMERIC | Nullable: True | Default: None
- **created_at**: NUMERIC | Nullable: True | Default: None
- **cycle_length**: INTEGER | Nullable: True | Default: None
- **daily_rewards**: TEXT | Nullable: True | Default: None
- **cycle_bonus_reward**: REAL | Nullable: True | Default: None
- **reset_streak_after_hours**: INTEGER | Nullable: True | Default: None
- **platform_url**: TEXT | Nullable: True | Default: None
- **platform_id**: TEXT | Nullable: True | Default: None
- **verify_key**: TEXT | Nullable: True | Default: None
- **max_verification_attempts**: INTEGER | Nullable: True | Default: None
**Primary Key:** []

**Foreign Keys:** None
**Indexes:** None

---

## Table: tasks_backup_20250202_163854

**Columns:**
- **id**: INTEGER | Nullable: True | Default: None
- **name**: TEXT | Nullable: True | Default: None
- **description**: TEXT | Nullable: True | Default: None
- **type**: TEXT | Nullable: True | Default: None
- **reward_type**: TEXT | Nullable: True | Default: None
- **reward_value**: REAL | Nullable: True | Default: None
- **target_value**: INTEGER | Nullable: True | Default: None
- **is_active**: NUMERIC | Nullable: True | Default: None
- **created_at**: NUMERIC | Nullable: True | Default: None
- **cycle_length**: INTEGER | Nullable: True | Default: None
- **daily_rewards**: TEXT | Nullable: True | Default: None
- **cycle_bonus_reward**: REAL | Nullable: True | Default: None
- **reset_streak_after_hours**: INTEGER | Nullable: True | Default: None
- **platform_url**: TEXT | Nullable: True | Default: None
- **platform_id**: TEXT | Nullable: True | Default: None
- **verify_key**: TEXT | Nullable: True | Default: None
- **max_verification_attempts**: INTEGER | Nullable: True | Default: None
**Primary Key:** []

**Foreign Keys:** None
**Indexes:** None

---

## Table: token_blacklist

**Columns:**
- **id**: INTEGER | Nullable: True | Default: None
- **token**: VARCHAR(500) | Nullable: False | Default: None
- **user_id**: INTEGER | Nullable: False | Default: None
- **expires_at**: TIMESTAMP | Nullable: False | Default: None
- **revoked_at**: TIMESTAMP | Nullable: False | Default: CURRENT_TIMESTAMP
- **revocation_reason**: VARCHAR(200) | Nullable: False | Default: None
- **revoked_by_ip**: VARCHAR(45) | Nullable: False | Default: None
**Primary Key:** ['id']

**Foreign Keys:**
- ['user_id'] -> users(['id'])
**Indexes:** None

---

## Table: token_blacklist_backup_20250205_003425

**Columns:**
- **id**: INTEGER | Nullable: True | Default: None
- **token**: TEXT | Nullable: True | Default: None
- **user_id**: INTEGER | Nullable: True | Default: None
- **expires_at**: NUMERIC | Nullable: True | Default: None
- **revoked_at**: NUMERIC | Nullable: True | Default: None
- **revocation_reason**: TEXT | Nullable: True | Default: None
- **revoked_by_ip**: TEXT | Nullable: True | Default: None
**Primary Key:** []

**Foreign Keys:** None
**Indexes:** None

---

## Table: token_blacklist_backup_20250205_003436

**Columns:**
- **id**: INTEGER | Nullable: True | Default: None
- **token**: TEXT | Nullable: True | Default: None
- **user_id**: INTEGER | Nullable: True | Default: None
- **expires_at**: NUMERIC | Nullable: True | Default: None
- **revoked_at**: NUMERIC | Nullable: True | Default: None
- **revocation_reason**: TEXT | Nullable: True | Default: None
- **revoked_by_ip**: TEXT | Nullable: True | Default: None
**Primary Key:** []

**Foreign Keys:** None
**Indexes:** None

---

## Table: token_blacklist_backup_20250205_003440

**Columns:**
- **id**: INTEGER | Nullable: True | Default: None
- **token**: TEXT | Nullable: True | Default: None
- **user_id**: INTEGER | Nullable: True | Default: None
- **expires_at**: NUMERIC | Nullable: True | Default: None
- **revoked_at**: NUMERIC | Nullable: True | Default: None
- **revocation_reason**: TEXT | Nullable: True | Default: None
- **revoked_by_ip**: TEXT | Nullable: True | Default: None
**Primary Key:** []

**Foreign Keys:** None
**Indexes:** None

---

## Table: token_blacklist_backup_20250205_003446

**Columns:**
- **id**: INTEGER | Nullable: True | Default: None
- **token**: TEXT | Nullable: True | Default: None
- **user_id**: INTEGER | Nullable: True | Default: None
- **expires_at**: NUMERIC | Nullable: True | Default: None
- **revoked_at**: NUMERIC | Nullable: True | Default: None
- **revocation_reason**: TEXT | Nullable: True | Default: None
- **revoked_by_ip**: TEXT | Nullable: True | Default: None
**Primary Key:** []

**Foreign Keys:** None
**Indexes:** None

---

## Table: token_blacklist_backup_20250205_003450

**Columns:**
- **id**: INTEGER | Nullable: True | Default: None
- **token**: TEXT | Nullable: True | Default: None
- **user_id**: INTEGER | Nullable: True | Default: None
- **expires_at**: NUMERIC | Nullable: True | Default: None
- **revoked_at**: NUMERIC | Nullable: True | Default: None
- **revocation_reason**: TEXT | Nullable: True | Default: None
- **revoked_by_ip**: TEXT | Nullable: True | Default: None
**Primary Key:** []

**Foreign Keys:** None
**Indexes:** None

---

## Table: token_blacklist_backup_20250205_003721

**Columns:**
- **id**: INTEGER | Nullable: True | Default: None
- **token**: TEXT | Nullable: True | Default: None
- **user_id**: INTEGER | Nullable: True | Default: None
- **expires_at**: NUMERIC | Nullable: True | Default: None
- **revoked_at**: NUMERIC | Nullable: True | Default: None
- **revocation_reason**: TEXT | Nullable: True | Default: None
- **revoked_by_ip**: TEXT | Nullable: True | Default: None
**Primary Key:** []

**Foreign Keys:** None
**Indexes:** None

---

## Table: token_blacklist_backup_20250205_003737

**Columns:**
- **id**: INTEGER | Nullable: True | Default: None
- **token**: TEXT | Nullable: True | Default: None
- **user_id**: INTEGER | Nullable: True | Default: None
- **expires_at**: NUMERIC | Nullable: True | Default: None
- **revoked_at**: NUMERIC | Nullable: True | Default: None
- **revocation_reason**: TEXT | Nullable: True | Default: None
- **revoked_by_ip**: TEXT | Nullable: True | Default: None
**Primary Key:** []

**Foreign Keys:** None
**Indexes:** None

---

## Table: transactions

**Columns:**
- **id**: INTEGER | Nullable: False | Default: None
- **type**: VARCHAR(21) | Nullable: False | Default: None
- **amount**: FLOAT | Nullable: False | Default: None
- **created_at**: DATETIME | Nullable: False | Default: None
- **user_id**: INTEGER | Nullable: False | Default: None
- **reseller_id**: INTEGER | Nullable: True | Default: None
- **subscription_id**: INTEGER | Nullable: True | Default: None
- **package_name**: VARCHAR(100) | Nullable: True | Default: None
- **package_price**: FLOAT | Nullable: True | Default: None
- **package_data_limit**: BIGINT | Nullable: True | Default: None
- **package_expire_days**: INTEGER | Nullable: True | Default: None
- **description**: TEXT | Nullable: True | Default: None
- **balance_after**: FLOAT | Nullable: False | Default: None
- **status**: VARCHAR(20) | Nullable: False | Default: None
**Primary Key:** ['id']

**Foreign Keys:**
- ['user_id'] -> users(['id'])
- ['reseller_id'] -> users(['id'])
- ['subscription_id'] -> vpn_subscriptions(['id'])
**Indexes:**
- idx_transactions_created: Columns: ['created_at'] | Unique: 0
- idx_transactions_created_at: Columns: ['created_at'] | Unique: 0
- idx_transactions_reseller: Columns: ['reseller_id'] | Unique: 0
- idx_transactions_reseller_id: Columns: ['reseller_id'] | Unique: 0
- idx_transactions_subscription: Columns: ['subscription_id'] | Unique: 0
- idx_transactions_subscription_id: Columns: ['subscription_id'] | Unique: 0
- idx_transactions_type: Columns: ['type'] | Unique: 0
- idx_transactions_user: Columns: ['user_id'] | Unique: 0
- idx_transactions_user_id: Columns: ['user_id'] | Unique: 0
- ix_transactions_id: Columns: ['id'] | Unique: 0

---

## Table: users

**Columns:**
- **id**: INTEGER | Nullable: False | Default: None
- **telegram_id**: INTEGER | Nullable: True | Default: None
- **username**: VARCHAR(50) | Nullable: False | Default: None
- **hashed_password**: VARCHAR(255) | Nullable: True | Default: None
- **role**: VARCHAR(8) | Nullable: False | Default: None
- **wallet_balance**: FLOAT | Nullable: False | Default: None
- **created_at**: DATETIME | Nullable: False | Default: None
- **first_name**: VARCHAR(50) | Nullable: True | Default: None
- **last_name**: VARCHAR(50) | Nullable: True | Default: None
- **email**: VARCHAR(100) | Nullable: True | Default: None
- **is_active**: BOOLEAN | Nullable: False | Default: None
- **telegram_photo_url**: TEXT | Nullable: True | Default: None
- **discount_percent**: FLOAT | Nullable: False | Default: None
- **marzban_username**: VARCHAR(100) | Nullable: True | Default: None
- **marzban_subscription_url**: TEXT | Nullable: True | Default: None
- **referred_by**: INTEGER | Nullable: True | Default: None
- **referral_code**: VARCHAR(8) | Nullable: False | Default: None
- **device_platform**: VARCHAR(50) | Nullable: True | Default: None
- **device_id**: VARCHAR(50) | Nullable: True | Default: None
- **last_login**: DATETIME | Nullable: True | Default: None
**Primary Key:** ['id']

**Foreign Keys:**
- ['referred_by'] -> users(['id'])
**Indexes:**
- ix_users_id: Columns: ['id'] | Unique: 0
- ix_users_telegram_id: Columns: ['telegram_id'] | Unique: 1
- ix_users_username: Columns: ['username'] | Unique: 1

---

## Table: users_backup_20250202_011845

**Columns:**
- **id**: INTEGER | Nullable: True | Default: None
- **telegram_id**: INTEGER | Nullable: True | Default: None
- **username**: TEXT | Nullable: True | Default: None
- **hashed_password**: TEXT | Nullable: True | Default: None
- **role**: TEXT | Nullable: True | Default: None
- **wallet_balance**: REAL | Nullable: True | Default: None
- **created_at**: NUMERIC | Nullable: True | Default: None
- **first_name**: TEXT | Nullable: True | Default: None
- **last_name**: TEXT | Nullable: True | Default: None
- **email**: TEXT | Nullable: True | Default: None
- **is_active**: NUMERIC | Nullable: True | Default: None
- **telegram_photo_url**: TEXT | Nullable: True | Default: None
- **discount_percent**: REAL | Nullable: True | Default: None
- **marzban_username**: TEXT | Nullable: True | Default: None
- **marzban_subscription_url**: TEXT | Nullable: True | Default: None
- **referred_by**: INTEGER | Nullable: True | Default: None
- **referral_code**: TEXT | Nullable: True | Default: None
- **device_platform**: TEXT | Nullable: True | Default: None
- **device_id**: TEXT | Nullable: True | Default: None
- **last_login**: NUMERIC | Nullable: True | Default: None
**Primary Key:** []

**Foreign Keys:** None
**Indexes:** None

---

## Table: vpn_packages

**Columns:**
- **id**: INTEGER | Nullable: False | Default: None
- **name**: VARCHAR(100) | Nullable: False | Default: None
- **data_limit**: BIGINT | Nullable: False | Default: None
- **expire_days**: INTEGER | Nullable: False | Default: None
- **price**: FLOAT | Nullable: False | Default: None
- **description**: TEXT | Nullable: True | Default: None
- **created_at**: DATETIME | Nullable: False | Default: None
- **is_active**: BOOLEAN | Nullable: False | Default: None
**Primary Key:** ['id']

**Foreign Keys:** None
**Indexes:**
- ix_vpn_packages_id: Columns: ['id'] | Unique: 0

---

## Table: vpn_subscriptions

**Columns:**
- **id**: INTEGER | Nullable: False | Default: None
- **user_id**: INTEGER | Nullable: False | Default: None
- **package_id**: INTEGER | Nullable: False | Default: None
- **marzban_username**: VARCHAR(100) | Nullable: False | Default: None
- **subscription_url**: TEXT | Nullable: False | Default: None
- **created_at**: DATETIME | Nullable: False | Default: None
- **expires_at**: DATETIME | Nullable: False | Default: None
- **data_limit**: BIGINT | Nullable: False | Default: None
- **data_used**: BIGINT | Nullable: False | Default: None
- **is_active**: BOOLEAN | Nullable: False | Default: None
**Primary Key:** ['id']

**Foreign Keys:**
- ['user_id'] -> users(['id'])
- ['package_id'] -> vpn_packages(['id'])
**Indexes:**
- idx_vpn_subs_expires: Columns: ['expires_at'] | Unique: 0
- idx_vpn_subs_package: Columns: ['package_id'] | Unique: 0
- idx_vpn_subs_user: Columns: ['user_id'] | Unique: 0
- idx_vpn_subscriptions_expires_at: Columns: ['expires_at'] | Unique: 0
- idx_vpn_subscriptions_package_id: Columns: ['package_id'] | Unique: 0
- idx_vpn_subscriptions_user_id: Columns: ['user_id'] | Unique: 0
- ix_vpn_subscriptions_id: Columns: ['id'] | Unique: 0

---
