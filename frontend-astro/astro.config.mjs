// @ts-check
import { defineConfig } from 'astro/config';
import react from '@astrojs/react';
import tailwind from '@astrojs/tailwind';

// https://astro.build/config
export default defineConfig({
  integrations: [
    react(),
    tailwind({
      applyBaseStyles: false, // We'll use custom base styles
    })
  ],
  output: 'static',
  build: {
    inlineStylesheets: 'auto',
    assets: 'assets',
  },
  vite: {
    build: {
      target: 'es2020',
      minify: 'esbuild',
      cssMinify: true,
      rollupOptions: {
        output: {
          manualChunks: {
            'telegram': ['@telegram-apps/sdk-react'],
            'ui': ['framer-motion', 'react-hot-toast'],
          }
        }
      }
    },
    ssr: {
      noExternal: ['@telegram-apps/sdk-react']
    }
  },
  server: {
    host: '0.0.0.0',
    port: 3001, // Different port to avoid conflict
  },
  devToolbar: {
    enabled: false // Disable for cleaner mobile experience
  }
});
