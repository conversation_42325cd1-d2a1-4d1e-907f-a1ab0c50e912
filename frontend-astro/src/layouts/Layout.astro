---
export interface Props {
  title: string;
  description?: string;
}

const { title, description = "VIPVerse - Secure VIP Verse" } = Astro.props;
---

<!doctype html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="description" content={description} />
    <meta name="viewport" content="width=device-width, initial-scale=1, viewport-fit=cover" />
    <meta name="theme-color" content="#030303" />
    <meta http-equiv="X-Content-Type-Options" content="nosniff">
    <meta name="robots" content="noindex, nofollow">
    
    <!-- Telegram WebApp Script - Critical -->
    <script src="https://telegram.org/js/telegram-web-app.js" is:inline></script>
    
    <!-- Preconnect to critical domains -->
    <link rel="preconnect" href="https://telegram.org" crossorigin>
    
    <link rel="icon" type="image/png" href="/favicon.png" />
    <title>{title}</title>
    
    <!-- Critical CSS inlined -->
    <style is:inline>
      *, *::before, *::after { 
        box-sizing: border-box; 
        border-width: 0; 
        border-style: solid; 
        border-color: currentColor; 
      }
      
      html, body { 
        margin: 0; 
        padding: 0; 
        width: 100%; 
        height: 100%; 
        overflow: hidden; 
        background: #030303; 
        color: #e0e0e0; 
        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif; 
        -webkit-font-smoothing: antialiased; 
        -moz-osx-font-smoothing: grayscale; 
      }

      #root { 
        min-height: 100vh; 
      }
      
      .loading-screen { 
        position: fixed; 
        inset: 0; 
        display: flex; 
        flex-direction: column; 
        align-items: center; 
        justify-content: center; 
        background: #030303; 
        z-index: 99999; 
      }
    </style>
  </head>
  <body>
    <!-- Loading screen -->
    <div class="loading-screen" id="loading">
      <div class="text-4xl mb-4">🚀</div>
      <h1 class="text-2xl font-bold text-white mb-2">VIPVerse</h1>
      <p class="text-purple-400">Loading...</p>
    </div>
    
    <!-- Main content -->
    <div id="root">
      <slot />
    </div>
    
    <!-- Telegram initialization -->
    <script is:inline>
      // Initialize Telegram WebApp
      if (window.Telegram?.WebApp) {
        const tg = window.Telegram.WebApp;
        tg.ready();
        tg.expand();
        
        // Hide loading screen after initialization
        setTimeout(() => {
          const loading = document.getElementById('loading');
          if (loading) {
            loading.style.opacity = '0';
            setTimeout(() => loading.remove(), 300);
          }
        }, 1000);
      } else {
        // Fallback for non-Telegram environments
        setTimeout(() => {
          const loading = document.getElementById('loading');
          if (loading) loading.remove();
        }, 500);
      }
    </script>
  </body>
</html> 