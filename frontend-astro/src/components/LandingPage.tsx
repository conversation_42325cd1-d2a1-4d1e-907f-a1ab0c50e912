import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';

interface TelegramWebApp {
  ready: () => void;
  expand: () => void;
  initData: string;
  initDataUnsafe: any;
}

declare global {
  interface Window {
    Telegram?: {
      WebApp: TelegramWebApp;
    };
  }
}

const LandingPage: React.FC = () => {
  const [isLoading, setIsLoading] = useState(true);
  const [isTelegramReady, setIsTelegramReady] = useState(false);

  useEffect(() => {
    // Initialize Telegram WebApp
    const initTelegram = () => {
      if (window.Telegram?.WebApp) {
        const tg = window.Telegram.WebApp;
        tg.ready();
        tg.expand();
        setIsTelegramReady(true);
        console.log('Telegram WebApp initialized');
      }
      setIsLoading(false);
    };

    // Check if Telegram is already available
    if (window.Telegram?.WebApp) {
      initTelegram();
    } else {
      // Wait for Telegram script to load
      const checkTelegram = setInterval(() => {
        if (window.Telegram?.WebApp) {
          initTelegram();
          clearInterval(checkTelegram);
        }
      }, 100);

      // Timeout after 3 seconds
      setTimeout(() => {
        clearInterval(checkTelegram);
        setIsLoading(false);
      }, 3000);
    }
  }, []);

  const handleGetStarted = () => {
    // Navigate to dashboard or trigger authentication
    console.log('Get started clicked');
    // You can add navigation logic here
  };

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-gray-900 to-black flex items-center justify-center">
        <div className="text-center">
          <div className="text-4xl mb-4 animate-bounce">🚀</div>
          <h1 className="text-2xl font-bold text-white mb-2">VIPVerse</h1>
          <p className="text-purple-400">Initializing...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-900 via-black to-purple-900 flex items-center justify-center p-4">
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6 }}
        className="text-center max-w-md w-full"
      >
        {/* Logo/Icon */}
        <motion.div
          initial={{ scale: 0 }}
          animate={{ scale: 1 }}
          transition={{ delay: 0.2, type: "spring", stiffness: 200 }}
          className="text-6xl mb-8"
        >
          🚀
        </motion.div>

        {/* Title */}
        <motion.h1
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ delay: 0.4 }}
          className="text-4xl font-bold text-white mb-4 tracking-tight"
        >
          VIPVerse
        </motion.h1>

        {/* Subtitle */}
        <motion.p
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ delay: 0.6 }}
          className="text-lg text-gray-300 mb-8"
        >
          Welcome to the future of secure VIP experiences
        </motion.p>

        {/* Status */}
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ delay: 0.8 }}
          className="mb-8"
        >
          {isTelegramReady ? (
            <div className="flex items-center justify-center text-green-400">
              <svg className="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
              </svg>
              Telegram Connected
            </div>
          ) : (
            <div className="text-yellow-400">
              ⚠️ Telegram WebApp not detected
            </div>
          )}
        </motion.div>

        {/* Get Started Button */}
        <motion.button
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 1 }}
          whileHover={{ scale: 1.05 }}
          whileTap={{ scale: 0.95 }}
          onClick={handleGetStarted}
          className="w-full py-4 px-8 bg-gradient-to-r from-purple-600 to-blue-600 text-white font-semibold rounded-xl shadow-lg hover:shadow-xl transition-all duration-200"
        >
          Get Started
        </motion.button>

        {/* Development Info */}
        {import.meta.env.DEV && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: 1.2 }}
            className="mt-6 p-3 bg-black/30 rounded-lg text-left text-xs text-gray-400"
          >
            <p><strong>Dev Info:</strong></p>
            <p>Telegram: {isTelegramReady ? 'Ready' : 'Not Available'}</p>
            <p>Environment: {import.meta.env.MODE}</p>
          </motion.div>
        )}
      </motion.div>
    </div>
  );
};

export default LandingPage; 